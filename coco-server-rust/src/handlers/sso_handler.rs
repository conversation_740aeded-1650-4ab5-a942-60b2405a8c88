use axum::{
    extract::{Query, State},
    http::StatusCode,
    response::{Html, IntoResponse},
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::{error, info};
use uuid::Uuid;

use crate::config::config_manager::ConfigManager;
use crate::error::result::AxumResult;
use crate::handlers::account_handler::{generate_jwt_token, get_user_info};

// SSO登录请求参数
#[derive(Debug, Deserialize)]
pub struct SsoLoginQuery {
    pub provider: Option<String>,
    pub product: Option<String>,
    pub request_id: Option<String>,
}

// SSO登录响应
#[derive(Debug, Serialize)]
pub struct SsoLoginResponse {
    pub access_token: String,
    pub expire_in: i64,
    pub request_id: String,
    pub provider: String,
}

/// SSO登录处理器
/// 处理 /sso/login/cloud 路径的GET请求
pub async fn sso_login_handler(
    Query(params): Query<SsoLoginQuery>,
    State(config_manager): State<Arc<ConfigManager>>,
) -> AxumResult<impl IntoResponse> {
    info!("Handling SSO login request: {:?}", params);

    let provider = params.provider.unwrap_or_else(|| "coco-cloud".to_string());
    let product = params.product.unwrap_or_else(|| "coco".to_string());
    let request_id = params
        .request_id
        .unwrap_or_else(|| Uuid::new_v4().to_string());

    // 获取用户信息并生成访问令牌
    match get_user_info().await {
        Ok(user_info) => {
            match generate_jwt_token(&user_info).await {
                Ok(token_data) => {
                    // 构建重定向URL
                    let redirect_url = format!(
                        "coco://oauth_callback?code={}&request_id={}&provider={}expire_in={}",
                        token_data.access_token, request_id, provider, token_data.expire_in
                    );

                    // 生成HTML页面，包含自动重定向和手动链接
                    let html_content = generate_sso_success_page(&redirect_url, &request_id);

                    info!("SSO login successful, redirecting to: {}", redirect_url);
                    Ok((StatusCode::OK, Html(html_content)))
                }
                Err(e) => {
                    error!("Failed to generate JWT token: {}", e);
                    let error_html = generate_sso_error_page("Failed to generate access token");
                    Ok((StatusCode::INTERNAL_SERVER_ERROR, Html(error_html)))
                }
            }
        }
        Err(e) => {
            error!("Failed to get user info: {}", e);
            let error_html = generate_sso_error_page("Failed to get user information");
            Ok((StatusCode::INTERNAL_SERVER_ERROR, Html(error_html)))
        }
    }
}

/// 生成SSO成功页面HTML
fn generate_sso_success_page(redirect_url: &str, _request_id: &str) -> String {
    format!(
        r#"
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coco AI - 登录成功</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }}
        .container {{
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }}
        .success-icon {{
            color: #4CAF50;
            font-size: 48px;
            margin-bottom: 20px;
        }}
        h1 {{
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
        }}
        .subtitle {{
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }}
        .redirect-info {{
            background: #f5f5f5;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-size: 14px;
            color: #555;
        }}
        .manual-link {{
            display: inline-block;
            background: #007AFF;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            margin: 10px 0;
            font-weight: 500;
            transition: background 0.3s;
        }}
        .manual-link:hover {{
            background: #0056CC;
        }}
        .copy-url {{
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            color: #495057;
        }}
        .countdown {{
            color: #007AFF;
            font-weight: bold;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">✓</div>
        <h1>登录成功！</h1>
        <p class="subtitle">正在重定向到 Coco AI 应用...</p>

        <div class="redirect-info">
            为了继续操作，如果 <span class="countdown" id="countdown">5</span> 秒内未自动重定向，请点击以下链接：
        </div>

        <a href="{}" class="manual-link" id="redirect-link">打开 Coco AI</a>

        <div class="redirect-info">
            如果重定向不起作用，您可以复制以下 URL 并将其粘贴到 Coco AI 的连接设置窗口中：
        </div>

        <div class="copy-url" id="copy-url">{}</div>
    </div>

    <script>
        let countdown = 5;
        const countdownElement = document.getElementById('countdown');
        const redirectLink = document.getElementById('redirect-link');

        // 更新倒计时
        const timer = setInterval(() => {{
            countdown--;
            countdownElement.textContent = countdown;

            if (countdown <= 0) {{
                clearInterval(timer);
                // 自动点击重定向链接
                redirectLink.click();
            }}
        }}, 1000);

        // 复制URL功能
        document.getElementById('copy-url').addEventListener('click', function() {{
            navigator.clipboard.writeText(this.textContent).then(() => {{
                this.style.background = '#d4edda';
                this.textContent = '已复制到剪贴板！';
                setTimeout(() => {{
                    this.style.background = '#f8f9fa';
                    this.textContent = '{}';
                }}, 2000);
            }});
        }});
    </script>
</body>
</html>
"#,
        redirect_url, redirect_url, redirect_url
    )
}

/// 生成SSO错误页面HTML
fn generate_sso_error_page(error_message: &str) -> String {
    format!(
        r#"
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coco AI - 登录失败</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 40px 20px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }}
        .container {{
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }}
        .error-icon {{
            color: #f44336;
            font-size: 48px;
            margin-bottom: 20px;
        }}
        h1 {{
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
        }}
        .error-message {{
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }}
        .retry-button {{
            display: inline-block;
            background: #007AFF;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            margin: 10px 0;
            font-weight: 500;
            transition: background 0.3s;
        }}
        .retry-button:hover {{
            background: #0056CC;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="error-icon">✗</div>
        <h1>登录失败</h1>
        <p class="error-message">{}</p>
        <a href="javascript:history.back()" class="retry-button">返回重试</a>
    </div>
</body>
</html>
"#,
        error_message
    )
}
